<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFFFFF">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 标题 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="SMB网络共享设置"
            android:textSize="20sp"
            android:textStyle="bold"
            android:textColor="#333333"
            android:layout_marginBottom="16dp" />

        <!-- SMB服务开关卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:elevation="2dp"
            android:radius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="SMB服务控制"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:layout_marginBottom="8dp" />

                <CheckBox
                    android:id="@+id/cb_enable_smb"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="启用SMB图片自动上传"
                    android:textColor="#666666"
                    android:textSize="14sp"
                    android:layout_marginBottom="8dp" />

                <CheckBox
                    android:id="@+id/cb_enable_smb_video"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="启用SMB视频自动上传"
                    android:textColor="#666666"
                    android:textSize="14sp" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 服务器配置卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:elevation="2dp"
            android:radius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="服务器配置"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:layout_marginBottom="12dp" />

                <!-- 服务器IP地址 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="wrap_content"
                        android:text="服务器IP："
                        android:textColor="#333333"
                        android:textSize="14sp" />

                    <EditText
                        android:id="@+id/et_server_ip"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:hint="*************"
                        android:inputType="textNoSuggestions"
                        android:textSize="14sp"
                        android:background="@android:drawable/edit_text" />

                </LinearLayout>

                <!-- 共享名称 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="wrap_content"
                        android:text="共享名称："
                        android:textColor="#333333"
                        android:textSize="14sp" />

                    <EditText
                        android:id="@+id/et_share_name"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:hint="share"
                        android:inputType="textNoSuggestions"
                        android:textSize="14sp"
                        android:background="@android:drawable/edit_text" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 认证信息卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:elevation="2dp"
            android:radius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="认证信息"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:layout_marginBottom="12dp" />

                <!-- 用户名 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="wrap_content"
                        android:text="用户名："
                        android:textColor="#333333"
                        android:textSize="14sp" />

                    <EditText
                        android:id="@+id/et_username"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:hint="留空表示匿名访问"
                        android:inputType="textNoSuggestions"
                        android:textSize="14sp"
                        android:background="@android:drawable/edit_text" />

                </LinearLayout>

                <!-- 密码 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="wrap_content"
                        android:text="密码："
                        android:textColor="#333333"
                        android:textSize="14sp" />

                    <EditText
                        android:id="@+id/et_password"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:hint="留空表示无密码"
                        android:inputType="textPassword"
                        android:textSize="14sp"
                        android:background="@android:drawable/edit_text" />

                </LinearLayout>

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 路径管理卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:elevation="2dp"
            android:radius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="路径管理"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:layout_marginBottom="12dp" />

                <!-- 远程路径选择 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="80dp"
                        android:layout_height="wrap_content"
                        android:text="远程路径："
                        android:textColor="#333333"
                        android:textSize="14sp" />

                    <Spinner
                        android:id="@+id/spinner_remote_path"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1" />

                </LinearLayout>

                <Button
                    android:id="@+id/btn_browse_path"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="浏览远程路径"
                    android:background="#2196F3"
                    android:textColor="#FFFFFF" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

        <!-- 连接控制卡片 -->
        <androidx.cardview.widget.CardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="12dp"
            android:elevation="2dp"
            android:radius="8dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="连接控制"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:textColor="#333333"
                    android:layout_marginBottom="12dp" />

                <!-- 连接状态 -->
                <TextView
                    android:id="@+id/tv_connection_status"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="连接状态：未连接"
                    android:textColor="#666666"
                    android:textSize="14sp"
                    android:layout_marginBottom="12dp" />

                <!-- 控制按钮 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <Button
                        android:id="@+id/btn_test_connection"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="测试连接"
                        android:background="#4CAF50"
                        android:textColor="#FFFFFF"
                        android:layout_marginEnd="8dp" />

                    <Button
                        android:id="@+id/btn_save_settings"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="保存设置"
                        android:background="#FF9800"
                        android:textColor="#FFFFFF"
                        android:layout_marginStart="8dp" />

                </LinearLayout>

                <!-- 进度条 -->
                <ProgressBar
                    android:id="@+id/progress_bar"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12dp"
                    android:visibility="gone"
                    style="?android:attr/progressBarStyleHorizontal" />

            </LinearLayout>

        </androidx.cardview.widget.CardView>

    </LinearLayout>

</ScrollView>
