<?xml version="1.0" encoding="utf-8"?>
<!-- 按钮按下动画效果 -->
<set xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 缩放动画 -->
    <objectAnimator
        android:propertyName="scaleX"
        android:duration="100"
        android:valueFrom="1.0"
        android:valueTo="0.95"
        android:valueType="floatType"
        android:interpolator="@android:interpolator/fast_out_slow_in" />
        
    <objectAnimator
        android:propertyName="scaleY"
        android:duration="100"
        android:valueFrom="1.0"
        android:valueTo="0.95"
        android:valueType="floatType"
        android:interpolator="@android:interpolator/fast_out_slow_in" />
        
    <!-- 透明度动画 -->
    <objectAnimator
        android:propertyName="alpha"
        android:duration="100"
        android:valueFrom="1.0"
        android:valueTo="0.8"
        android:valueType="floatType"
        android:interpolator="@android:interpolator/fast_out_slow_in" />
        
</set>
