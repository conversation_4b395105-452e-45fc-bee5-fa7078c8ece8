<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#AA333333"
    android:orientation="vertical"
    android:padding="16dp"
    android:minWidth="350dp">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="图片格式设置"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:textStyle="bold" />
    </LinearLayout>

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#44FFFFFF"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp" />

    <!-- 格式选择 -->
    <RadioGroup
        android:id="@+id/rg_image_format"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="8dp">

        <RadioButton
            android:id="@+id/rb_format_jpeg"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="JPEG格式 (推荐, 高压缩率)"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:padding="8dp"
            android:buttonTint="#FFFFFF"
            android:checked="true"/>

        <RadioButton
            android:id="@+id/rb_format_png"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="PNG格式 (无损, 支持透明)"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:padding="8dp"
            android:buttonTint="#FFFFFF"/>

        <RadioButton
            android:id="@+id/rb_format_bmp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="BMP格式 (无压缩, 文件最大)"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:padding="8dp"
            android:buttonTint="#FFFFFF"/>
    </RadioGroup>

    <!-- 分隔线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="#22FFFFFF"
        android:layout_marginTop="12dp"
        android:layout_marginBottom="12dp" />

    <!-- 说明文本 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="注: JPEG适合普通拍照, PNG适合需要细节的图像, BMP提供原始数据但文件较大"
        android:textColor="#AAFFFFFF"
        android:textSize="12sp"
        android:padding="8dp"/>

    <!-- 按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp">

        <Button
            android:id="@+id/btn_cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="取消"
            android:textColor="#FFFFFF"
            android:background="#44888888"
            android:layout_marginEnd="8dp"/>

        <Button
            android:id="@+id/btn_save"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="保存"
            android:textColor="#FFFFFF"
            android:background="#4488AAFF"
            android:layout_marginStart="8dp"/>
    </LinearLayout>
</LinearLayout> 