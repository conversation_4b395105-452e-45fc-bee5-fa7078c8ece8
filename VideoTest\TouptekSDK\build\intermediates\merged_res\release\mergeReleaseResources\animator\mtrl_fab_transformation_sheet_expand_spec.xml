<?xml version="1.0" encoding="utf-8"?>
<!--
  Copyright 2017 The Android Open Source Project

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->
<!-- Deprecated. Use
     com.google.android.material.transition.MaterialContainerTransform
     instead. -->
<set xmlns:android="http://schemas.android.com/apk/res/android">

  <objectAnimator
      android:propertyName="elevation"
      android:startOffset="0"
      android:duration="150" />

  <objectAnimator
      android:propertyName="translationXLinear"
      android:startOffset="0"
      android:duration="345" />
  <objectAnimator
      android:propertyName="translationXCurveUpwards"
      android:startOffset="0"
      android:duration="150" />
  <objectAnimator
      android:propertyName="translationXCurveDownwards"
      android:startOffset="0"
      android:duration="345" />

  <objectAnimator
      android:propertyName="translationYLinear"
      android:startOffset="0"
      android:duration="345" />
  <objectAnimator
      android:propertyName="translationYCurveUpwards"
      android:startOffset="0"
      android:duration="345" />
  <objectAnimator
      android:propertyName="translationYCurveDownwards"
      android:startOffset="0"
      android:duration="150" />

  <objectAnimator
      android:propertyName="iconFade"
      android:startOffset="0"
      android:duration="120" />
  <objectAnimator
      android:propertyName="expansion"
      android:startOffset="45"
      android:duration="255" />
  <objectAnimator
      android:propertyName="color"
      android:startOffset="75"
      android:duration="75" />
  <objectAnimator
      android:propertyName="contentFade"
      android:startOffset="150"
      android:duration="150" />

</set>
