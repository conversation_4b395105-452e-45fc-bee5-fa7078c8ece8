[{"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/glitch_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/glitch_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/duotone_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/duotone_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/surface_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/surface_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/rgb_saturation_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/rgb_saturation_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/circle_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/circle_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/swirl_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/swirl_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/chroma_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/chroma_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/simple_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/simple_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/saturation_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/saturation_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/analog_tv_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/analog_tv_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/lamoish_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/lamoish_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/contrast_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/contrast_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/image70s_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/image70s_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/sepia_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/sepia_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/object_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/object_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/object_vertex.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/object_vertex.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/negative_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/negative_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/snow_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/snow_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/sharpness_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/sharpness_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/rainbow_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/rainbow_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/beauty_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/beauty_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/brightness_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/brightness_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/pixelated_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/pixelated_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/money_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/money_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/temperature_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/temperature_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/edge_detection_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/edge_detection_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/gamma_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/gamma_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/blur_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/blur_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/android_view_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/android_view_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/fxaa.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/fxaa.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/fire_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/fire_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/ripple_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/ripple_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/color_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/color_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/halftone_lines_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/halftone_lines_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/zebra_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/zebra_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/earlybird_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/earlybird_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/basic_deformation_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/basic_deformation_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/camera_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/camera_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/simple_vertex.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/simple_vertex.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/cartoon_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/cartoon_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/polygonization_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/polygonization_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/fxaa_pc.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/fxaa_pc.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/black_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/black_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/exposure_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/exposure_fragment.glsl"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/raw/grey_scale_fragment.glsl", "source": "com.android.rockchip.video.CodecUtils-encoder-2.3.5-18:/raw/grey_scale_fragment.glsl"}]