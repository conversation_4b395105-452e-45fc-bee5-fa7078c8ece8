<?xml version="1.0" encoding="utf-8"?>
<!-- 现代化视频控制按钮背景样式 -->
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="#40FFFFFF">
    
    <item android:id="@android:id/background">
        <selector>
            <!-- 按下状态 -->
            <item android:state_pressed="true">
                <shape android:shape="rectangle">
                    <solid android:color="#FF404040" />
                    <corners android:radius="16dp" />
                    <stroke android:width="2dp" android:color="#99FFFFFF" />
                </shape>
            </item>

            <!-- 禁用状态 -->
            <item android:state_enabled="false">
                <shape android:shape="rectangle">
                    <solid android:color="#FF1A1A1A" />
                    <corners android:radius="16dp" />
                    <stroke android:width="1dp" android:color="#33FFFFFF" />
                </shape>
            </item>
            
            <!-- 正常状态 -->
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="#FF2A2A2A" />
                    <corners android:radius="16dp" />
                    <stroke android:width="1dp" android:color="#66FFFFFF" />
                </shape>
            </item>
        </selector>
    </item>
    
</ripple>
