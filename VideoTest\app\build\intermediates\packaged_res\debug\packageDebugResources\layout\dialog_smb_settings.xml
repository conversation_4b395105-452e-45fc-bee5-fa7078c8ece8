<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#AA333333">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        android:minWidth="400dp">

        <!-- 标题栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="SMB服务器设置"
                android:textColor="#FFFFFF"
                android:textSize="16sp"
                android:textStyle="bold" />
        </LinearLayout>

        <!-- 分隔线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#44FFFFFF"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp" />

        <!-- 启用选项 -->
        <CheckBox
            android:id="@+id/cb_enable_smb"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="启用SMB图片自动上传"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:padding="8dp"
            android:buttonTint="#FFFFFF"/>

        <CheckBox
            android:id="@+id/cb_enable_smb_video"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="启用SMB视频自动上传"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:padding="8dp"
            android:buttonTint="#FFFFFF"/>

        <!-- 分隔线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#22FFFFFF"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="4dp" />

        <!-- 服务器设置 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="服务器设置"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:textStyle="bold"
            android:padding="8dp"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="服务器IP:"
                android:textColor="#FFFFFF"
                android:textSize="14sp"/>

            <EditText
                android:id="@+id/et_server_ip"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="例如: *************"
                android:textColorHint="#88FFFFFF"
                android:textColor="#FFFFFF"
                android:padding="8dp"
                android:background="#22FFFFFF"
                android:layout_marginStart="8dp"
                android:singleLine="true"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="用户名:"
                android:textColor="#FFFFFF"
                android:textSize="14sp"/>

            <EditText
                android:id="@+id/et_username"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="留空表示匿名访问"
                android:textColorHint="#88FFFFFF"
                android:textColor="#FFFFFF"
                android:padding="8dp"
                android:background="#22FFFFFF"
                android:layout_marginStart="8dp"
                android:singleLine="true"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="密码:"
                android:textColor="#FFFFFF"
                android:textSize="14sp"/>

            <EditText
                android:id="@+id/et_password"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="留空表示匿名访问"
                android:textColorHint="#88FFFFFF"
                android:textColor="#FFFFFF"
                android:padding="8dp"
                android:background="#22FFFFFF"
                android:layout_marginStart="8dp"
                android:inputType="textPassword"
                android:singleLine="true"/>
        </LinearLayout>

        <!-- 分隔线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#22FFFFFF"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="4dp" />

        <!-- 共享设置 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="共享设置"
            android:textColor="#FFFFFF"
            android:textSize="14sp"
            android:textStyle="bold"
            android:padding="8dp"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="共享名称:"
                android:textColor="#FFFFFF"
                android:textSize="14sp"/>

            <EditText
                android:id="@+id/et_share_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:hint="例如: share"
                android:textColorHint="#88FFFFFF"
                android:textColor="#FFFFFF"
                android:padding="8dp"
                android:background="#22FFFFFF"
                android:layout_marginStart="8dp"
                android:singleLine="true"/>
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:padding="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="远程路径:"
                android:textColor="#FFFFFF"
                android:textSize="14sp"/>

            <FrameLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:layout_marginEnd="8dp">

                <Spinner
                    android:id="@+id/spinner_remote_path"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="#22FFFFFF"
                    android:padding="8dp"
                    android:popupBackground="#AA333333"
                    android:spinnerMode="dropdown"
                    android:dropDownWidth="match_parent"/>

                <ProgressBar
                    android:id="@+id/progress_loading_paths"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_gravity="end|center_vertical"
                    android:layout_marginEnd="8dp"
                    android:visibility="gone"/>
            </FrameLayout>

            <Button
                android:id="@+id/btn_refresh_paths"
                android:layout_width="wrap_content"
                android:layout_height="36dp"
                android:text="刷新"
                android:textSize="12sp"
                android:background="#555555"
                android:textColor="#FFFFFF"
                android:padding="4dp"/>
        </LinearLayout>

        <!-- 分隔线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="#22FFFFFF"
            android:layout_marginTop="12dp"
            android:layout_marginBottom="12dp" />

        <!-- 按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="8dp">

            <Button
                android:id="@+id/btn_test_connection"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="测试连接"
                android:textColor="#FFFFFF"
                android:background="#555555"
                android:layout_marginEnd="8dp"/>

            <Button
                android:id="@+id/btn_save"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="保存设置"
                android:textColor="#FFFFFF"
                android:background="#555555"/>
        </LinearLayout>

    </LinearLayout>
</ScrollView> 