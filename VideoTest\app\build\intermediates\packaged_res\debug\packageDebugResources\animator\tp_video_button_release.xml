<?xml version="1.0" encoding="utf-8"?>
<!-- 按钮释放动画效果 -->
<set xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 缩放恢复动画 -->
    <objectAnimator
        android:propertyName="scaleX"
        android:duration="150"
        android:valueFrom="0.95"
        android:valueTo="1.0"
        android:valueType="floatType"
        android:interpolator="@android:interpolator/fast_out_slow_in" />
        
    <objectAnimator
        android:propertyName="scaleY"
        android:duration="150"
        android:valueFrom="0.95"
        android:valueTo="1.0"
        android:valueType="floatType"
        android:interpolator="@android:interpolator/fast_out_slow_in" />
        
    <!-- 透明度恢复动画 -->
    <objectAnimator
        android:propertyName="alpha"
        android:duration="150"
        android:valueFrom="0.8"
        android:valueTo="1.0"
        android:valueType="floatType"
        android:interpolator="@android:interpolator/fast_out_slow_in" />
        
</set>
