#Wed Jul 30 14:16:44 CST 2025
com.touptek.TouptekSDK-main-5\:/color/tp_video_button_text_color.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\color\\tp_video_button_text_color.xml
com.touptek.TouptekSDK-main-5\:/drawable/ic_fast_forward_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_fast_forward_white_24.xml
com.touptek.TouptekSDK-main-5\:/drawable/ic_fast_rewind_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_fast_rewind_white_24.xml
com.touptek.TouptekSDK-main-5\:/drawable/ic_pause_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_pause_white_24.xml
com.touptek.TouptekSDK-main-5\:/drawable/ic_play_arrow_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_play_arrow_white_24.xml
com.touptek.TouptekSDK-main-5\:/drawable/ic_settings_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_settings_white_24.xml
com.touptek.TouptekSDK-main-5\:/drawable/ic_skip_next_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_skip_next_white_24.xml
com.touptek.TouptekSDK-main-5\:/drawable/ic_skip_previous_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_skip_previous_white_24.xml
com.touptek.TouptekSDK-main-5\:/drawable/ic_step_frame_white_24.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\ic_step_frame_white_24.xml
com.touptek.TouptekSDK-main-5\:/drawable/tp_speed_dropdown_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\tp_speed_dropdown_background.xml
com.touptek.TouptekSDK-main-5\:/drawable/tp_speed_item_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\tp_speed_item_background.xml
com.touptek.TouptekSDK-main-5\:/drawable/tp_speed_item_selected_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\tp_speed_item_selected_background.xml
com.touptek.TouptekSDK-main-5\:/drawable/tp_video_button_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\tp_video_button_background.xml
com.touptek.TouptekSDK-main-5\:/drawable/tp_video_controls_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\tp_video_controls_background.xml
com.touptek.TouptekSDK-main-5\:/drawable/tp_video_play_button_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\tp_video_play_button_background.xml
com.touptek.TouptekSDK-main-5\:/drawable/tp_video_progress_drawable.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\tp_video_progress_drawable.xml
com.touptek.TouptekSDK-main-5\:/drawable/tp_video_progress_thumb.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\tp_video_progress_thumb.xml
com.touptek.TouptekSDK-main-5\:/drawable/tp_video_settings_button_background.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\drawable\\tp_video_settings_button_background.xml
com.touptek.TouptekSDK-main-5\:/layout/dialog_tp_test.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\dialog_tp_test.xml
com.touptek.TouptekSDK-main-5\:/layout/tp_speed_dropdown_menu.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\tp_speed_dropdown_menu.xml
com.touptek.TouptekSDK-main-5\:/layout/tp_video_player_controls.xml=C\:\\hhx\\rk3588\\AndroidStudio\\VideoTest\\TouptekSDK\\build\\intermediates\\packaged_res\\release\\packageReleaseResources\\layout\\tp_video_player_controls.xml
