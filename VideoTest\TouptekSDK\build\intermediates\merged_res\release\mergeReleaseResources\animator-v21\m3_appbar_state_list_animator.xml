<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2016 The Android Open Source Project

     Licensed under the Apache License, Version 2.0 (the "License");
     you may not use this file except in compliance with the License.
     You may obtain a copy of the License at

          http://www.apache.org/licenses/LICENSE-2.0

     Unless required by applicable law or agreed to in writing, software
     distributed under the License is distributed on an "AS IS" BASIS,
     WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     See the License for the specific language governing permissions and
     limitations under the License.
-->
<selector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

  <item
      android:state_enabled="true"
      app:state_lifted="false"
      app:state_liftable="true">
    <objectAnimator
        android:duration="@integer/app_bar_elevation_anim_duration"
        android:propertyName="elevation"
        android:valueTo="@dimen/m3_comp_top_app_bar_small_container_elevation"
        android:valueType="floatType"/>
  </item>

  <item android:state_enabled="true">
    <objectAnimator
        android:duration="@integer/app_bar_elevation_anim_duration"
        android:propertyName="elevation"
        android:valueTo="@dimen/m3_comp_top_app_bar_small_on_scroll_container_elevation"
        android:valueType="floatType"/>
  </item>

  <item>
    <objectAnimator
        android:duration="0"
        android:propertyName="elevation"
        android:valueTo="@dimen/m3_comp_top_app_bar_small_container_elevation"
        android:valueType="floatType"/>
  </item>

</selector>
