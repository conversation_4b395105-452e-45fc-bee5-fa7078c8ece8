<?xml version="1.0" encoding="utf-8"?>
<!-- 播放速度菜单项选中状态背景样式 -->
<ripple xmlns:android="http://schemas.android.com/apk/res/android"
    android:color="#40FFFFFF">
    
    <item android:id="@android:id/background">
        <selector>
            <!-- 按下状态 -->
            <item android:state_pressed="true">
                <shape android:shape="rectangle">
                    <solid android:color="#FF555555" />
                    <corners android:radius="12dp" />
                    <stroke android:width="2dp" android:color="#FFFFFFFF" />
                </shape>
            </item>
            
            <!-- 选中状态（正常） -->
            <item>
                <shape android:shape="rectangle">
                    <solid android:color="#FF4A4A4A" />
                    <corners android:radius="12dp" />
                    <stroke android:width="2dp" android:color="#FFFFFFFF" />
                </shape>
            </item>
        </selector>
    </item>
    
</ripple>
