[{"merged": "com.touptek.TouptekSDK-release-35:/drawable-v23/m3_tabs_background.xml", "source": "com.touptek.TouptekSDK-material-1.12.0-17:/drawable-v23/m3_tabs_background.xml"}, {"merged": "com.touptek.TouptekSDK-release-35:/drawable-v23/m3_radiobutton_ripple.xml", "source": "com.touptek.TouptekSDK-material-1.12.0-17:/drawable-v23/m3_radiobutton_ripple.xml"}, {"merged": "com.touptek.TouptekSDK-release-35:/drawable-v23/abc_control_background_material.xml", "source": "com.touptek.TouptekSDK-appcompat-1.7.0-16:/drawable-v23/abc_control_background_material.xml"}, {"merged": "com.touptek.TouptekSDK-release-35:/drawable-v23/m3_selection_control_ripple.xml", "source": "com.touptek.TouptekSDK-material-1.12.0-17:/drawable-v23/m3_selection_control_ripple.xml"}, {"merged": "com.touptek.TouptekSDK-release-35:/drawable-v23/mtrl_popupmenu_background_overlay.xml", "source": "com.touptek.TouptekSDK-material-1.12.0-17:/drawable-v23/mtrl_popupmenu_background_overlay.xml"}, {"merged": "com.touptek.TouptekSDK-release-35:/drawable-v23/m3_tabs_transparent_background.xml", "source": "com.touptek.TouptekSDK-material-1.12.0-17:/drawable-v23/m3_tabs_transparent_background.xml"}]