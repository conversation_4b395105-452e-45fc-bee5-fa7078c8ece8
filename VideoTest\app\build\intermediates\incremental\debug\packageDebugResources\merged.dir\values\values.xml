<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string-array name="image_format_options">
        <item>JPEG</item>
        <item>PNG</item>
        <item>BMP</item>
    </string-array>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_name">VideoTest</string>
    <style name="Base.Theme.MediacodecNew" parent="Theme.Material3.DayNight.NoActionBar">
        
        
    </style>
    <style name="FullScreenDialog" parent="Theme.AppCompat.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowSoftInputMode">adjustResize</item>
    </style>
    <style name="Theme.MediacodecNew" parent="Base.Theme.MediacodecNew"/>
    <style name="TpVideoControlButton" parent="Widget.AppCompat.ImageButton">
        <item name="android:layout_width">56dp</item>
        <item name="android:layout_height">56dp</item>
        <item name="android:background">@drawable/tp_video_button_background</item>
        <item name="android:elevation">4dp</item>
        <item name="android:stateListAnimator">@null</item>
        <item name="android:layout_marginEnd">12dp</item>
        <item name="android:scaleType">center</item>
        <item name="android:contentDescription">控制按钮</item>
    </style>
    <style name="TpVideoPlayButton" parent="TpVideoControlButton">
        <item name="android:layout_width">64dp</item>
        <item name="android:layout_height">64dp</item>
        <item name="android:background">@drawable/tp_video_play_button_background</item>
        <item name="android:elevation">6dp</item>
        <item name="android:contentDescription">播放/暂停</item>
    </style>
    <style name="TpVideoProgressBar" parent="Widget.AppCompat.SeekBar">
        <item name="android:progressDrawable">@drawable/tp_video_progress_drawable</item>
        <item name="android:thumb">@drawable/tp_video_progress_thumb</item>
        <item name="android:paddingTop">24dp</item>
        <item name="android:paddingBottom">24dp</item>
        <item name="android:paddingLeft">16dp</item>
        <item name="android:paddingRight">16dp</item>
        <item name="android:thumbOffset">0dp</item>
    </style>
    <style name="TpVideoSettingsButton" parent="TpVideoControlButton">
        <item name="android:background">@drawable/tp_video_settings_button_background</item>
        <item name="android:layout_marginEnd">0dp</item>
        <item name="android:contentDescription">设置</item>
    </style>
    <style name="TpVideoSpeedDialog" parent="Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:windowAnimationStyle">@style/TpVideoSpeedDialogAnimation</item>
    </style>
    <style name="TpVideoSpeedDialogAnimation">
        <item name="android:windowEnterAnimation">@anim/tp_speed_dialog_enter</item>
        <item name="android:windowExitAnimation">@anim/tp_speed_dialog_exit</item>
    </style>
    <style name="TpVideoSpeedDropdownAnimation">
        <item name="android:windowEnterAnimation">@anim/tp_speed_dropdown_enter</item>
        <item name="android:windowExitAnimation">@anim/tp_speed_dropdown_exit</item>
    </style>
    <style name="TpVideoSpeedDropdownItem">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">40dp</item>
        <item name="android:textColor">#FFFFFFFF</item>
        <item name="android:textSize">13sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/tp_speed_item_background</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
        <item name="android:layout_marginBottom">2dp</item>
        <item name="android:paddingHorizontal">12dp</item>
    </style>
    <style name="TpVideoSpeedItem">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:textColor">#FFFFFFFF</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:background">@drawable/tp_speed_item_background</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
        <item name="android:layout_marginBottom">4dp</item>
    </style>
    <style name="TpVideoTimeDisplay" parent="Widget.AppCompat.TextView">
        <item name="android:textColor">#FFFFFFFF</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:gravity">center</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:shadowColor">#80000000</item>
        <item name="android:shadowDx">1</item>
        <item name="android:shadowDy">1</item>
        <item name="android:shadowRadius">2</item>
    </style>
    <style name="VideoControlButton" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/tp_video_button_background</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">12sp</item>
        <item name="android:minWidth">0dp</item>
        <item name="android:minHeight">0dp</item>
        <item name="android:padding">8dp</item>
    </style>
    <style name="VideoSpeedButton" parent="Widget.AppCompat.Button">
        <item name="android:background">@drawable/tp_speed_item_background</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">10sp</item>
        <item name="android:minWidth">0dp</item>
        <item name="android:minHeight">0dp</item>
        <item name="android:padding">4dp</item>
    </style>
    <declare-styleable name="TpImageView">
        <attr format="float" name="maxScale"/>
        <attr format="boolean" name="zoomEnabled"/>
        <attr format="boolean" name="panEnabled"/>
        <attr format="boolean" name="doubleTapEnabled"/>
    </declare-styleable>
    <declare-styleable name="TpVideoPlayerView">
        <attr format="boolean" name="autoPlay"/>
    </declare-styleable>
</resources>