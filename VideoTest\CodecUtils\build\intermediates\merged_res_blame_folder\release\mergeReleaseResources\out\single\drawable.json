[{"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/ic_m3_chip_close.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/ic_m3_chip_close.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_dialog_background.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_dialog_background.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/m3_avd_hide_password.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/m3_avd_hide_password.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_bottomsheet_drag_handle.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_bottomsheet_drag_handle.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_cab_background_internal_bg.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_cab_background_internal_bg.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/avd_hide_password.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/avd_hide_password.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_checkbox_button_icon_unchecked_indeterminate.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_checkbox_button_icon_unchecked_indeterminate.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/ic_play_arrow_white_24.xml", "source": "com.android.rockchip.video.CodecUtils-main-36:/drawable/ic_play_arrow_white_24.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/m3_tabs_rounded_line_indicator.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/m3_tabs_rounded_line_indicator.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/btn_checkbox_checked_mtrl.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/btn_checkbox_checked_mtrl.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/tp_video_progress_thumb.xml", "source": "com.android.rockchip.video.CodecUtils-main-36:/drawable/tp_video_progress_thumb.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/ic_m3_chip_check.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/ic_m3_chip_check.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/tp_video_play_button_background.xml", "source": "com.android.rockchip.video.CodecUtils-main-36:/drawable/tp_video_play_button_background.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/material_ic_menu_arrow_up_black_24dp.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/material_ic_menu_arrow_up_black_24dp.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_vector_test.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-resources-1.7.0-24:/drawable/abc_vector_test.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/tp_video_progress_drawable.xml", "source": "com.android.rockchip.video.CodecUtils-main-36:/drawable/tp_video_progress_drawable.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/ic_m3_chip_checked_circle.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/ic_m3_chip_checked_circle.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_star_half_black_48dp.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_star_half_black_48dp.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_checkbox_button_icon.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_checkbox_button_icon.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/ic_fast_rewind_white_24.xml", "source": "com.android.rockchip.video.CodecUtils-main-36:/drawable/ic_fast_rewind_white_24.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/ic_mtrl_checked_circle.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/ic_mtrl_checked_circle.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_popupmenu_background.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_popupmenu_background.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/ic_mtrl_chip_checked_circle.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/ic_mtrl_chip_checked_circle.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/design_password_eye.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/design_password_eye.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_ratingbar_small_material.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_ratingbar_small_material.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/m3_avd_show_password.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/m3_avd_show_password.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_btn_borderless_material.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_btn_borderless_material.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_ic_checkbox_checked.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_ic_checkbox_checked.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_switch_thumb_checked.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_switch_thumb_checked.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_item_background_holo_light.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_item_background_holo_light.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/btn_radio_off_mtrl.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/btn_radio_off_mtrl.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/ic_clear_black_24.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/ic_clear_black_24.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_ic_menu_share_mtrl_alpha.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_ic_menu_share_mtrl_alpha.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_ic_menu_selectall_mtrl_alpha.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_ic_menu_selectall_mtrl_alpha.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_ic_indeterminate.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_ic_indeterminate.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/notification_bg.xml", "source": "com.android.rockchip.video.CodecUtils-core-1.13.0-27:/drawable/notification_bg.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_ic_menu_cut_mtrl_alpha.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_ic_menu_cut_mtrl_alpha.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_switch_thumb.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_switch_thumb.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_list_selector_background_transition_holo_dark.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_list_selector_background_transition_holo_dark.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_ratingbar_indicator_material.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_ratingbar_indicator_material.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/test_level_drawable.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/test_level_drawable.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_list_selector_holo_dark.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_list_selector_holo_dark.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_switch_thumb_pressed.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_switch_thumb_pressed.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/notification_icon_background.xml", "source": "com.android.rockchip.video.CodecUtils-core-1.13.0-27:/drawable/notification_icon_background.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_checkbox_button_unchecked_checked.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_checkbox_button_unchecked_checked.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/tooltip_frame_light.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/tooltip_frame_light.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/ic_pause_white_24.xml", "source": "com.android.rockchip.video.CodecUtils-main-36:/drawable/ic_pause_white_24.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/btn_checkbox_unchecked_mtrl.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/btn_checkbox_unchecked_mtrl.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_ic_ab_back_material.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_ic_ab_back_material.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_btn_radio_material.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_btn_radio_material.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_ic_voice_search_api_material.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_ic_voice_search_api_material.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_switch_thumb_unchecked_pressed.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_switch_thumb_unchecked_pressed.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_checkbox_button_checked_unchecked.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_checkbox_button_checked_unchecked.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_ic_arrow_drop_right_black_24dp.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_ic_arrow_drop_right_black_24dp.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_ratingbar_material.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_ratingbar_material.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/design_ic_visibility_off.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/design_ic_visibility_off.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/indeterminate_static.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/indeterminate_static.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_ic_check_mark.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_ic_check_mark.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/material_ic_clear_black_24dp.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/material_ic_clear_black_24dp.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_ic_menu_overflow_material.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_ic_menu_overflow_material.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/btn_radio_on_to_off_mtrl_animation.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/btn_radio_on_to_off_mtrl_animation.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_item_background_holo_dark.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_item_background_holo_dark.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/material_ic_calendar_black_24dp.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/material_ic_calendar_black_24dp.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/notification_bg_low.xml", "source": "com.android.rockchip.video.CodecUtils-core-1.13.0-27:/drawable/notification_bg_low.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_ic_error.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_ic_error.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_dropdown_arrow.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_dropdown_arrow.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_ic_arrow_drop_down.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_ic_arrow_drop_down.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/tooltip_frame_dark.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/tooltip_frame_dark.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_btn_check_material.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_btn_check_material.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/tp_speed_item_background.xml", "source": "com.android.rockchip.video.CodecUtils-main-36:/drawable/tp_speed_item_background.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_ic_clear_material.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_ic_clear_material.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_ic_menu_copy_mtrl_am_alpha.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_ic_menu_copy_mtrl_am_alpha.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/tp_video_controls_background.xml", "source": "com.android.rockchip.video.CodecUtils-main-36:/drawable/tp_video_controls_background.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/ic_mtrl_chip_checked_black.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/ic_mtrl_chip_checked_black.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_ic_cancel.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_ic_cancel.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_checkbox_button_icon_unchecked_checked.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_checkbox_button_icon_unchecked_checked.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_switch_thumb_unchecked_checked.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_switch_thumb_unchecked_checked.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_seekbar_tick_mark_material.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_seekbar_tick_mark_material.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_switch_track.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_switch_track.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_ic_checkbox_unchecked.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_ic_checkbox_unchecked.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_ic_search_api_material.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_ic_search_api_material.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_checkbox_button_icon_checked_unchecked.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_checkbox_button_icon_checked_unchecked.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_switch_thumb_checked_unchecked.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_switch_thumb_checked_unchecked.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_btn_check_material_anim.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_btn_check_material_anim.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_ic_menu_paste_mtrl_am_alpha.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_ic_menu_paste_mtrl_am_alpha.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_tab_indicator_material.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_tab_indicator_material.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_checkbox_button.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_checkbox_button.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_cab_background_top_material.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_cab_background_top_material.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/m3_password_eye.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/m3_password_eye.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/notification_tile_bg.xml", "source": "com.android.rockchip.video.CodecUtils-core-1.13.0-27:/drawable/notification_tile_bg.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_text_cursor_material.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_text_cursor_material.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/tp_video_settings_button_background.xml", "source": "com.android.rockchip.video.CodecUtils-main-36:/drawable/tp_video_settings_button_background.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_star_black_48dp.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_star_black_48dp.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/navigation_empty_icon.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/navigation_empty_icon.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_list_selector_holo_light.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_list_selector_holo_light.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_btn_radio_material_anim.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_btn_radio_material_anim.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_textfield_search_material.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_textfield_search_material.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/material_ic_menu_arrow_down_black_24dp.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/material_ic_menu_arrow_down_black_24dp.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/design_snackbar_background.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/design_snackbar_background.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/m3_bottom_sheet_drag_handle.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/m3_bottom_sheet_drag_handle.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_switch_thumb_checked_pressed.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_switch_thumb_checked_pressed.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_btn_default_mtrl_shape.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_btn_default_mtrl_shape.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_switch_thumb_unchecked.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_switch_thumb_unchecked.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/design_fab_background.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/design_fab_background.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/ic_step_frame_white_24.xml", "source": "com.android.rockchip.video.CodecUtils-main-36:/drawable/ic_step_frame_white_24.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_tabs_default_indicator.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_tabs_default_indicator.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_checkbox_button_icon_indeterminate_checked.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_checkbox_button_icon_indeterminate_checked.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/ic_clock_black_24dp.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/ic_clock_black_24dp.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/ic_skip_next_white_24.xml", "source": "com.android.rockchip.video.CodecUtils-main-36:/drawable/ic_skip_next_white_24.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_switch_thumb_pressed_checked.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_switch_thumb_pressed_checked.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/ic_settings_white_24.xml", "source": "com.android.rockchip.video.CodecUtils-main-36:/drawable/ic_settings_white_24.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_switch_thumb_pressed_unchecked.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_switch_thumb_pressed_unchecked.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_checkbox_button_icon_indeterminate_unchecked.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_checkbox_button_icon_indeterminate_unchecked.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_switch_track_decoration.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_switch_track_decoration.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/m3_tabs_line_indicator.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/m3_tabs_line_indicator.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/avd_show_password.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/avd_show_password.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/design_ic_visibility.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/design_ic_visibility.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/btn_radio_on_mtrl.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/btn_radio_on_mtrl.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_switch_thumb_material.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_switch_thumb_material.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/material_ic_edit_black_24dp.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/material_ic_edit_black_24dp.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/ic_mtrl_chip_close_circle.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/ic_mtrl_chip_close_circle.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_ic_arrow_drop_up.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_ic_arrow_drop_up.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/material_ic_keyboard_arrow_right_black_24dp.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/material_ic_keyboard_arrow_right_black_24dp.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_list_selector_background_transition_holo_light.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_list_selector_background_transition_holo_light.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_seekbar_thumb_material.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_seekbar_thumb_material.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/ic_skip_previous_white_24.xml", "source": "com.android.rockchip.video.CodecUtils-main-36:/drawable/ic_skip_previous_white_24.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_seekbar_track_material.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_seekbar_track_material.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/tp_video_button_background.xml", "source": "com.android.rockchip.video.CodecUtils-main-36:/drawable/tp_video_button_background.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/btn_checkbox_checked_to_unchecked_mtrl_animation.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/btn_checkbox_checked_to_unchecked_mtrl_animation.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_ic_go_search_api_material.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_ic_go_search_api_material.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/ic_fast_forward_white_24.xml", "source": "com.android.rockchip.video.CodecUtils-main-36:/drawable/ic_fast_forward_white_24.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/tp_speed_dropdown_background.xml", "source": "com.android.rockchip.video.CodecUtils-main-36:/drawable/tp_speed_dropdown_background.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/ic_search_black_24.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/ic_search_black_24.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/ic_arrow_back_black_24.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/ic_arrow_back_black_24.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/m3_popupmenu_background_overlay.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/m3_popupmenu_background_overlay.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/btn_checkbox_unchecked_to_checked_mtrl_animation.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/btn_checkbox_unchecked_to_checked_mtrl_animation.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/btn_radio_off_to_on_mtrl_animation.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/btn_radio_off_to_on_mtrl_animation.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/mtrl_checkbox_button_icon_checked_indeterminate.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/mtrl_checkbox_button_icon_checked_indeterminate.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/ic_keyboard_black_24dp.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/ic_keyboard_black_24dp.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/tp_speed_item_selected_background.xml", "source": "com.android.rockchip.video.CodecUtils-main-36:/drawable/tp_speed_item_selected_background.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/material_ic_keyboard_arrow_left_black_24dp.xml", "source": "com.android.rockchip.video.CodecUtils-material-1.12.0-20:/drawable/material_ic_keyboard_arrow_left_black_24dp.xml"}, {"merged": "com.android.rockchip.video.CodecUtils-release-35:/drawable/abc_spinner_textfield_background_material.xml", "source": "com.android.rockchip.video.CodecUtils-appcompat-1.7.0-6:/drawable/abc_spinner_textfield_background_material.xml"}]