<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:padding="16dp"
    android:background="?android:attr/selectableItemBackground"
    android:gravity="center_vertical"
    android:minHeight="56dp">

    <ImageView
        android:id="@+id/menu_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:src="@android:drawable/ic_menu_manage"
        android:tint="#666666"
        android:layout_marginEnd="16dp" />

    <TextView
        android:id="@+id/menu_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:text="设置项"
        android:textSize="16sp"
        android:textColor="#333333" />

    <ImageView
        android:id="@+id/menu_arrow"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:src="@android:drawable/ic_media_play"
        android:rotation="270"
        android:tint="#999999"
        android:layout_marginStart="8dp" />

</LinearLayout>
