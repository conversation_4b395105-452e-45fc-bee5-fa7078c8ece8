{"logs": [{"outputFile": "com.android.rockchip.mediacodecnew.app-mergeDebugResources-36:/values-v28/values-v28.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\737b03f0fc1ad0fd308d8e116bf71918\\transformed\\material-1.12.0\\res\\values-v28\\values-v28.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,249,344,441,529,617,705,793,880,967,1054,1141", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,17", "endColumns": "97,95,94,96,87,87,87,87,86,86,86,86,10", "endOffsets": "148,244,339,436,524,612,700,788,875,962,1049,1136,1423"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,24", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,153,249,344,441,529,617,705,793,880,967,1054,1675", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,27", "endColumns": "97,95,94,96,87,87,87,87,86,86,86,86,10", "endOffsets": "148,244,339,436,524,612,700,788,875,962,1049,1136,1957"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\5ee0973f6a00ce88be66ccc82fc087bc\\transformed\\appcompat-1.7.0\\res\\values-v28\\values-v28.xml", "from": {"startLines": "2,3,4,8", "startColumns": "4,4,4,4", "startOffsets": "55,130,217,397", "endLines": "2,3,7,11", "endColumns": "74,86,12,12", "endOffsets": "125,212,392,584"}, "to": {"startLines": "14,15,16,20", "startColumns": "4,4,4,4", "startOffsets": "1141,1216,1303,1483", "endLines": "14,15,19,23", "endColumns": "74,86,12,12", "endOffsets": "1211,1298,1478,1670"}}]}]}